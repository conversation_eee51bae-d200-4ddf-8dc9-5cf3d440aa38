{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a5603ad4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from scipy.stats import norm\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# Parâmetros da distribuição normal\n", "media = 134.8\n", "dp = 4.1\n", "limite = 130\n", "\n", "# Gerando valores para o eixo x (maior resolução para visualização)\n", "x = np.linspace(media - 4*dp, media + 4*dp, 400)\n", "\n", "# Calculando a distribuição normal\n", "y = norm.pdf(x, media, dp)\n", "\n", "# Probabilidade acumulada até o limite\n", "prob = norm.cdf(limite, loc=media, scale=dp)\n", "\n", "# Plotando o gráfico da densidade\n", "plt.figure(figsize=(8,4))\n", "plt.plot(x, y, color='#1f77b4')\n", "\n", "# Região a sombrear (x < limite)\n", "mask = x <= limite\n", "plt.fill_between(x[mask], y[mask], color='#1f77b4', alpha=0.35, label=f\"P(X < {limite}) = {prob:.3f}\")\n", "\n", "# Linha vertical no ponto limite\n", "plt.axvline(limite, color='red', linestyle='--', linewidth=1)\n", "\n", "# Anotação do valor de Z e probabilidade\n", "z = (limite - media)/dp\n", "plt.text(limite, max(y)*0.6, f\"Z = {z:.2f}\\nP = {prob:.3f}\", color='red', ha='right', va='center', bbox=dict(facecolor='white', edgecolor='red', alpha=0.7))\n", "\n", "plt.title('Distribuição Normal (média=134.8, DP=4.1)')\n", "plt.xlabel('Valor')\n", "plt.ylabel('Densidade de probabilidade')\n", "plt.legend(loc='upper right')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 1, "id": "d09ff019", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Z = (130 - 134.8)/4.1 = -1.1707\n", "Probabilidade P(X < 130) = 0.1209 (≈ 12.1%)\n"]}], "source": ["# Função para calcular P(X < valor_limite) dado média e desvio-padrão de uma Normal\n", "from scipy.stats import norm\n", "\n", "def prob_vendas_inferior(valor_limite: float, media: float, dp: float) -> float:\n", "    \"\"\"Retorna a probabilidade P(X < valor_limite) para X ~ Normal(media, dp).\n", "    Parâmetros:\n", "        valor_limite: valor para o qual se deseja a probabilidade acumulada.\n", "        media: média da distribuição normal.\n", "        dp: des<PERSON>-pad<PERSON><PERSON> da distribuição normal (deve ser > 0).\n", "    \"\"\"\n", "    if dp <= 0:\n", "        raise ValueError(\"O desvio-padrão deve ser positivo.\")\n", "    z = (valor_limite - media) / dp\n", "    return norm.cdf(z)\n", "\n", "# Exemplo solicitado: probabilidade de prever valor de vendas < 130\n", "media_exemplo = 134.8\n", "dp_exemplo = 4.1\n", "valor_limite_exemplo = 130\n", "prob = prob_vendas_inferior(valor_limite_exemplo, media_exemplo, dp_exemplo)\n", "print(f\"Z = ({valor_limite_exemplo} - {media_exemplo})/{dp_exemplo} = {(valor_limite_exemplo - media_exemplo)/dp_exemplo:.4f}\")\n", "print(f\"Probabilidade P(X < {valor_limite_exemplo}) = {prob:.4f} (≈ {prob*100:.1f}%)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}