import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Carregar os dados (assumindo que o arquivo CSV está na pasta assets)
# Ajuste o caminho conforme necessário
df = pd.read_csv('../assets/dados.csv')

# Tracking de linhas removidas por cada filtro
filtros_aplicados = []
rows_inicial = len(df)
filtros_aplicados.append(('Dados iniciais', rows_inicial, 0))

# Se o dado for igual a 'DEVOLUCAO DE MERCADORIA' na coluna 'Natureza_Operacao', ignoraremos
df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']
rows_after_devolucao = len(df)
removidas_devolucao = rows_inicial - rows_after_devolucao
filtros_aplicados.append(('Remoção DEVOLUCAO DE MERCADORIA', rows_after_devolucao, removidas_devolucao))

# Na coluna Preco_Varejo vamos ignorar valores menores que 5
df = df[df['Preco_Varejo'] > 1]
rows_after_preco_varejo = len(df)
removidas_preco_varejo = rows_after_devolucao - rows_after_preco_varejo
filtros_aplicados.append(('Preço Varejo > 1', rows_after_preco_varejo, removidas_preco_varejo))

df = df[df['Total_Preco_Varejo'] > 1]
rows_after_total_preco = len(df)
removidas_total_preco = rows_after_preco_varejo - rows_after_total_preco
filtros_aplicados.append(('Total Preço Varejo > 1', rows_after_total_preco, removidas_total_preco))

# Remover linhas que não conferem este requisito: Quantidade * Preco_Varejo - DESCONTO_CALCULADO = Valor_Total
df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
df = df[df['diferenca'] < 1]
rows_after_diferenca = len(df)
removidas_diferenca = rows_after_total_preco - rows_after_diferenca
filtros_aplicados.append(('Validação cálculo valor total', rows_after_diferenca, removidas_diferenca))

# 3. Filtrar clientes com datas de nascimento incoerentes (ex: idade < 10 ou > 100 anos)
df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')
df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')
df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')

df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]
rows_after_idade = len(df)
removidas_idade = rows_after_diferenca - rows_after_idade
filtros_aplicados.append(('Idade entre 10 e 100 anos', rows_after_idade, removidas_idade))

# 3.1. Filtrar clientes que tinham menos de 18 anos quando foram cadastrados
df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[df['idade_cadastro'] >= 18]
rows_after_idade_cadastro = len(df)
removidas_idade_cadastro = rows_after_idade - rows_after_idade_cadastro
filtros_aplicados.append(('Idade >= 18 anos no cadastro', rows_after_idade_cadastro, removidas_idade_cadastro))

# 4. Eliminar registros duplicados (mesmo DOC_UNICO, Produto, Cliente)
df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])
rows_after_duplicadas = len(df)
removidas_duplicadas = rows_after_idade_cadastro - rows_after_duplicadas
filtros_aplicados.append(('Remoção duplicatas', rows_after_duplicadas, removidas_duplicadas))

# 5. Validar consistência do valor total (já iniciado por você, mas com tolerância maior)
df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']
df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]
rows_after_check_total = len(df)
removidas_check_total = rows_after_duplicadas - rows_after_check_total
filtros_aplicados.append(('Validação valor total (tolerância 2)', rows_after_check_total, removidas_check_total))

# 6. Remover lojas sem identificação válida
df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]
rows_after_loja_valida = len(df)
removidas_loja_valida = rows_after_check_total - rows_after_loja_valida
filtros_aplicados.append(('Lojas com Tipo PDV válido', rows_after_loja_valida, removidas_loja_valida))

# 8. Produtos inválidos (sem nome, sem grupo ou com cod_auxiliar genérico)
df = df[df['Dim_Produtos.Nome'].notnull()]
rows_after_produto_nome = len(df)
removidas_produto_nome = rows_after_loja_valida - rows_after_produto_nome
filtros_aplicados.append(('Produtos com nome válido', rows_after_produto_nome, removidas_produto_nome))

df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]
rows_after_grupo_produto = len(df)
removidas_grupo_produto = rows_after_produto_nome - rows_after_grupo_produto
filtros_aplicados.append(('Produtos com grupo válido', rows_after_grupo_produto, removidas_grupo_produto))

# Corrigindo algumas colunas que tem espaços em branco no final
df['Dim_Lojas.Tipo_PDV'] = df['Dim_Lojas.Tipo_PDV'].astype(str).str.strip()
df['Dim_Produtos.Sub_Grupo'] = df['Dim_Produtos.Sub_Grupo'].astype(str).str.strip()

# Criando e exibindo a tabela de resumo dos filtros aplicados

df_filtros = pd.DataFrame(filtros_aplicados, columns=['Filtro Aplicado', 'Linhas Restantes', 'Linhas Removidas'])
df_filtros['% Removido'] = (df_filtros['Linhas Removidas'] / rows_inicial * 100).round(2)
df_filtros['% Restante'] = (df_filtros['Linhas Restantes'] / rows_inicial * 100).round(2)

print(df_filtros.to_string(index=False))

print(f"\nRESUMO FINAL:")
print(f"Linhas iniciais: {rows_inicial:,}")
print(f"Linhas finais: {rows_after_grupo_produto:,}")
print(f"Total de linhas removidas: {rows_inicial - rows_after_grupo_produto:,}")
print(f"Percentual de dados mantidos: {(rows_after_grupo_produto / rows_inicial * 100):.2f}%")
print(f"Percentual de dados removidos: {((rows_inicial - rows_after_grupo_produto) / rows_inicial * 100):.2f}%")