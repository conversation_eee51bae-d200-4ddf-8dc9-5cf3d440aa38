import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Configurar o estilo dos gráficos
plt.style.use('default')
sns.set_palette("husl")

# Carregar os dados (assumindo que o arquivo CSV está na pasta assets)
# Ajuste o caminho conforme necessário
df = pd.read_csv('../assets/dados.csv')

# Lista das colunas numéricas especificadas
colunas_numericas = ['Quantidade', 'Valor_Total', 'Preco_Varejo', 'DESCONTO_CALCULADO', 'Total_Preco_Varejo']

# Se o dado for igual a 'DEVOLUCAO DE MERCADORIA' na coluna 'Natureza_Operacao', ignoraremos
df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']

# Na coluna Preco_Varejo vamos ignorar valores menores que 5
df = df[df['Preco_Varejo'] > 1]
df = df[df['Total_Preco_Varejo'] > 1]

# Verificar se as colunas existem no dataset
colunas_existentes = [col for col in colunas_numericas if col in df.columns]
colunas_faltantes = [col for col in colunas_numericas if col not in df.columns]

if colunas_faltantes:
    print(f"Colunas não encontradas no dataset: {colunas_faltantes}")

print(f"Colunas encontradas: {colunas_existentes}")

# Criar subplots para os histogramas
fig, axes = plt.subplots(nrows=3, ncols=2, figsize=(15, 18))
fig.suptitle('Histogramas das Variáveis Numéricas - Chilli Beans', fontsize=16, fontweight='bold')

# Flatten axes para facilitar iteração
axes = axes.flatten()

# Gerar histograma para cada coluna
for i, coluna in enumerate(colunas_existentes):
    if i < len(axes):
        # Remover valores nulos para o cálculo
        dados_limpos = df[coluna].dropna()
        
        # Criar histograma
        axes[i].hist(dados_limpos, bins=50, alpha=0.7, color=sns.color_palette("husl")[i], edgecolor='black', linewidth=0.5)
        axes[i].set_title(f'Distribuição de {coluna}', fontsize=12, fontweight='bold')
        axes[i].set_xlabel(coluna)
        axes[i].set_ylabel('Frequência')
        axes[i].grid(True, alpha=0.3)
        
        # Adicionar estatísticas básicas no gráfico
        media = dados_limpos.mean()
        mediana = dados_limpos.median()
        axes[i].axvline(media, color='red', linestyle='--', alpha=0.7, label=f'Média: {media:.2f}')
        axes[i].axvline(mediana, color='orange', linestyle='--', alpha=0.7, label=f'Mediana: {mediana:.2f}')
        axes[i].legend()

# Remover subplot vazio se houver
if len(colunas_existentes) < len(axes):
    print (colunas_existentes)
    axes[-1].remove()

plt.tight_layout()
plt.show()

# Mostrar estatísticas descritivas
print("\n" + "="*80)
print("ESTATÍSTICAS DESCRITIVAS DAS VARIÁVEIS NUMÉRICAS")
print("="*80)

for coluna in colunas_existentes:
    print(f"\n{coluna}:")
    print("-" * 40)
    stats = df[coluna].describe()
    print(stats)
    print(f"Valores únicos: {df[coluna].nunique()}")
    print(f"Valores nulos: {df[coluna].isnull().sum()}")
    print(f"Porcentagem de nulos: {(df[coluna].isnull().sum() / len(df)) * 100:.2f}%")
    
    
# Preciso verificar quantas linhas de dados não conferem este requisito: Quantidade * Preco_Varejo - DESCONTO_CALCULADO = Valor_Total
df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
print(f"Linhas com diferença != 0: {df[df['diferenca'] != 0].shape[0]}")

# Análise de Normalidade das Distribuições
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("ANÁLISE DE NORMALIDADE DAS DISTRIBUIÇÕES")
print("="*80)

def analisar_normalidade(dados, nome_coluna):
    """
    Analisa a normalidade de uma distribuição usando múltiplos testes
    """
    print(f"\n{nome_coluna}:")
    print("-" * 50)
    
    # Remover valores nulos
    dados_limpos = dados.dropna()
    
    # Estatísticas descritivas básicas
    media = dados_limpos.mean()
    desvio = dados_limpos.std()
    skewness = stats.skew(dados_limpos)
    kurtosis = stats.kurtosis(dados_limpos)
    
    print(f"Média: {media:.2f}")
    print(f"Desvio Padrão: {desvio:.2f}")
    print(f"Assimetria (Skewness): {skewness:.3f}")
    print(f"Curtose (Kurtosis): {kurtosis:.3f}")
    
    # Interpretação da assimetria
    if abs(skewness) < 0.5:
        assimetria_interpretacao = "Aproximadamente simétrica"
    elif abs(skewness) < 1:
        assimetria_interpretacao = "Moderadamente assimétrica"
    else:
        assimetria_interpretacao = "Altamente assimétrica"
    
    # Interpretação da curtose
    if abs(kurtosis) < 0.5:
        curtose_interpretacao = "Aproximadamente mesocúrtica (normal)"
    elif kurtosis > 0.5:
        curtose_interpretacao = "Leptocúrtica (mais concentrada que normal)"
    else:
        curtose_interpretacao = "Platicúrtica (menos concentrada que normal)"
    
    print(f"Interpretação da Assimetria: {assimetria_interpretacao}")
    print(f"Interpretação da Curtose: {curtose_interpretacao}")
    
    # Testes de normalidade
    # Shapiro-Wilk (para amostras menores)
    if len(dados_limpos) <= 5000:
        shapiro_stat, shapiro_p = stats.shapiro(dados_limpos[:5000])
        print(f"Teste Shapiro-Wilk: estatística={shapiro_stat:.4f}, p-valor={shapiro_p:.6f}")
        shapiro_normal = shapiro_p > 0.05
    else:
        print("Amostra muito grande para Shapiro-Wilk")
        shapiro_normal = None
    
    # Kolmogorov-Smirnov
    ks_stat, ks_p = stats.kstest(dados_limpos, 'norm', args=(media, desvio))
    print(f"Teste Kolmogorov-Smirnov: estatística={ks_stat:.4f}, p-valor={ks_p:.6f}")
    ks_normal = ks_p > 0.05
    
    # Anderson-Darling
    ad_stat, ad_critical, ad_significance = stats.anderson(dados_limpos, dist='norm')
    print(f"Teste Anderson-Darling: estatística={ad_stat:.4f}")
    print(f"Valores críticos: {ad_critical}")
    print(f"Níveis de significância: {ad_significance}")
    
    # Verificar se é normal pelo Anderson-Darling (5% de significância)
    ad_normal = ad_stat < ad_critical[2]  # Índice 2 corresponde a 5%
    
    # D'Agostino-Pearson
    dp_stat, dp_p = stats.normaltest(dados_limpos)
    print(f"Teste D'Agostino-Pearson: estatística={dp_stat:.4f}, p-valor={dp_p:.6f}")
    dp_normal = dp_p > 0.05
    
    # Conclusão
    print("\nCONCLUSÃO:")
    testes_normais = []
    if shapiro_normal is not None:
        testes_normais.append(shapiro_normal)
    testes_normais.extend([ks_normal, ad_normal, dp_normal])
    
    proporcao_normal = sum(testes_normais) / len(testes_normais)
    
    if proporcao_normal >= 0.5:
        if abs(skewness) < 0.5 and abs(kurtosis) < 0.5:
            conclusao = "✅ DISTRIBUIÇÃO APROXIMADAMENTE NORMAL"
        else:
            conclusao = "⚠️ DISTRIBUIÇÃO PARCIALMENTE NORMAL (alguns testes indicam normalidade)"
    else:
        conclusao = "❌ DISTRIBUIÇÃO NÃO NORMAL"
    
    print(conclusao)
    
    return {
        'coluna': nome_coluna,
        'media': media,
        'desvio': desvio,
        'skewness': skewness,
        'kurtosis': kurtosis,
        'assimetria_interpretacao': assimetria_interpretacao,
        'curtose_interpretacao': curtose_interpretacao,
        'proporcao_testes_normal': proporcao_normal,
        'conclusao': conclusao
    }

# Analisar cada coluna
resultados = []
for coluna in colunas_existentes:
    resultado = analisar_normalidade(df[coluna], coluna)
    resultados.append(resultado)

# Resumo final
print("\n" + "="*80)
print("RESUMO GERAL - NORMALIDADE DAS DISTRIBUIÇÕES")
print("="*80)

colunas_normais = []
colunas_nao_normais = []

for resultado in resultados:
    if "APROXIMADAMENTE NORMAL" in resultado['conclusao']:
        colunas_normais.append(resultado['coluna'])
    else:
        colunas_nao_normais.append(resultado['coluna'])

print(f"\n📊 COLUNAS COM DISTRIBUIÇÃO APROXIMADAMENTE NORMAL:")
if colunas_normais:
    for coluna in colunas_normais:
        print(f"   ✅ {coluna}")
else:
    print("   Nenhuma coluna apresentou distribuição normal")

print(f"\n📊 COLUNAS SEM DISTRIBUIÇÃO NORMAL:")
if colunas_nao_normais:
    for coluna in colunas_nao_normais:
        print(f"   ❌ {coluna}")
else:
    print("   Todas as colunas apresentaram distribuição normal")

print(f"\n📈 CARACTERÍSTICAS OBSERVADAS:")
for resultado in resultados:
    print(f"   {resultado['coluna']}: {resultado['assimetria_interpretacao']}, {resultado['curtose_interpretacao']}")