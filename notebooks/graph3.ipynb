import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

# Adicionar o diretório pai ao path para importar o módulo de filtragem
sys.path.append(os.path.dirname(os.path.abspath('.')))

# Importar o módulo de filtragem
from data_filtering import apply_business_filters

# Carregar e filtrar os dados usando o módulo reutilizável
print('Carregando e aplicando filtros de negócio...')
df = apply_business_filters('../assets/dados.csv')

# Filtrar apenas os tipos de PDV desejados
tipos_pdv = ["LOJA DE RUA OTICO", "LOJA OTICO", "QUIOSQUE OTICO"]
df_oticas = df[df['Dim_Lojas.Tipo_PDV'].isin(tipos_pdv)]

# Criando uma tabela pivot: lojas x tipos de produtos com média das vendas
pivot = df_oticas.pivot_table(
    index='Dim_Lojas.Tipo_PDV',
    columns='Dim_Produtos.Sub_Grupo',
    values='Valor_Total',
    aggfunc='mean',
    fill_value=0
)

if pivot.size > 0:
    # Plotando o heatmap
    plt.figure(figsize=(14,8))
    
    # Annot para mostrar os valores
    # fmt=".0f" para mostrar apenas valores inteiros
    # cmap="YlGnBu" para usar uma paleta de roxo
    # cbar_kws={'label': 'Valor Total (R$)'} para adicionar um label à barra de cores
    sns.heatmap(pivot, annot=True, fmt=".0f", cmap="Purples", cbar_kws={'label': 'Valor Total (R$)'})
    
    plt.title("Valor Total de Vendas por Tipo de Loja e Produto")
    plt.xlabel("Produto")
    plt.ylabel("Loja")
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.show()
    
    print(f"\nTOP 5 produtos por valor médio (geral, sem separar por tipo de loja):")
    # Calculando a média diretamente dos dados originais, sem agregar por tipo de PDV
    product_mean = df_oticas.groupby('Dim_Produtos.Sub_Grupo')['Valor_Total'].mean().sort_values(ascending=False)
    print(product_mean.head())
else:
    print("ERRO: Tabela auxiliar vazia!")

# Opção 4: Quantidade total vendida (volume físico)
pivot_qty = df_oticas.pivot_table(
    index='Dim_Lojas.Tipo_PDV',
    columns='Dim_Produtos.Sub_Grupo',
    values='Quantidade',  # Usando quantidade em vez de valor
    aggfunc='sum',
    fill_value=0
)

plt.figure(figsize=(14,8))
sns.heatmap(pivot_qty, annot=True, fmt=".0f", cmap="Greens", cbar_kws={'label': 'Quantidade Total'})
plt.title("Volume de Produtos Vendidos por Tipo de Loja e Produto")
plt.xlabel("Produto")
plt.ylabel("Loja")
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

print(f"\nTOP 5 produtos por quantidade total vendida (geral, sem separar por tipo de loja):")
qty_totals = pivot_qty.sum(axis=0).sort_values(ascending=False)
print(qty_totals.head())