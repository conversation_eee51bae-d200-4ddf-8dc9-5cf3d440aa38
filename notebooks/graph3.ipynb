import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Carregar os dados (assumindo que o arquivo CSV está na pasta assets)
# Ajuste o caminho conforme necessário
df = pd.read_csv('../assets/dados.csv')

df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']
df = df[df['Preco_Varejo'] > 1]
df = df[df['Total_Preco_Varejo'] > 1]

df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
df = df[df['diferenca'] < 1]

df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')
df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')
df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')

df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]

df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[df['idade_cadastro'] >= 18]

df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])

df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']
df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]

df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]
df = df[df['Dim_Produtos.Nome'].notnull()]
df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]

df['Dim_Lojas.Tipo_PDV'] = df['Dim_Lojas.Tipo_PDV'].astype(str).str.strip()
df['Dim_Produtos.Sub_Grupo'] = df['Dim_Produtos.Sub_Grupo'].astype(str).str.strip()

# Filtrar apenas os tipos de PDV desejados
tipos_pdv = ["LOJA DE RUA OTICO", "LOJA OTICO", "QUIOSQUE OTICO"]
df_oticas = df[df['Dim_Lojas.Tipo_PDV'].isin(tipos_pdv)]

# Criando uma tabela pivot: lojas x tipos de produtos com média das vendas
pivot = df_oticas.pivot_table(
    index='Dim_Lojas.Tipo_PDV',
    columns='Dim_Produtos.Sub_Grupo',
    values='Valor_Total',
    aggfunc='mean',
    fill_value=0
)

if pivot.size > 0:
    # Plotando o heatmap
    plt.figure(figsize=(14,8))
    
    # Annot para mostrar os valores
    # fmt=".0f" para mostrar apenas valores inteiros
    # cmap="YlGnBu" para usar uma paleta de roxo
    # cbar_kws={'label': 'Valor Total (R$)'} para adicionar um label à barra de cores
    sns.heatmap(pivot, annot=True, fmt=".0f", cmap="Purples", cbar_kws={'label': 'Valor Total (R$)'})
    
    plt.title("Valor Total de Vendas por Tipo de Loja e Produto")
    plt.xlabel("Produto")
    plt.ylabel("Loja")
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.show()
    
    print(f"\nTOP 5 produtos por valor médio (geral, sem separar por tipo de loja):")
    # Calculando a média diretamente dos dados originais, sem agregar por tipo de PDV
    product_mean = df_oticas.groupby('Dim_Produtos.Sub_Grupo')['Valor_Total'].mean().sort_values(ascending=False)
    print(product_mean.head())
else:
    print("ERRO: Tabela auxiliar vazia!")

# Opção 4: Quantidade total vendida (volume físico)
pivot_qty = df_oticas.pivot_table(
    index='Dim_Lojas.Tipo_PDV',
    columns='Dim_Produtos.Sub_Grupo',
    values='Quantidade',  # Usando quantidade em vez de valor
    aggfunc='sum',
    fill_value=0
)

plt.figure(figsize=(14,8))
sns.heatmap(pivot_qty, annot=True, fmt=".0f", cmap="Greens", cbar_kws={'label': 'Quantidade Total'})
plt.title("Volume de Produtos Vendidos por Tipo de Loja e Produto")
plt.xlabel("Produto")
plt.ylabel("Loja")
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

print(f"\nTOP 5 produtos por quantidade total vendida (geral, sem separar por tipo de loja):")
qty_totals = pivot_qty.sum(axis=0).sort_values(ascending=False)
print(qty_totals.head())