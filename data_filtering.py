"""
<PERSON><PERSON><PERSON>lo de filtragem de dados para análise de vendas.

<PERSON>ste módulo contém as regras de negócio para limpeza e filtragem dos dados de vendas,
baseado no notebook filtering.ipynb. O objetivo é evitar repetição de código e
centralizar as regras de filtragem em um local reutilizável.

Principais filtros aplicados:
1. Remoção de devoluções de mercadoria
2. Validação de preços mínimos
3. Validação de cálculos de valor total
4. Filtros de idade dos clientes
5. Remoção de duplicatas
6. Validação de lojas e produtos
"""

import pandas as pd
import numpy as np

def apply_business_filters(csv_path, verbose=False):
    """
    Aplica todas as regras de negócio de filtragem aos dados de vendas.
    
    Args:
        csv_path (str): Caminho para o arquivo CSV com os dados
        verbose (bool): Se True, exibe estatísticas de cada filtro aplicado
        
    Returns:
        pd.DataFrame: DataFrame filtrado com as regras de negócio aplicadas
    """
    
    # Carregar os dados
    df = pd.read_csv(csv_path)
    
    if verbose:
        print(f"Dados iniciais: {len(df):,} registros")
    
    # Aplicar filtros sequencialmente
    df = _remove_returns(df, verbose)
    df = _validate_prices(df, verbose)
    df = _validate_total_calculations(df, verbose)
    df = _validate_customer_ages(df, verbose)
    df = _remove_duplicates(df, verbose)
    df = _validate_stores_and_products(df, verbose)
    df = _clean_string_columns(df, verbose)
    
    if verbose:
        print(f"\nDados finais: {len(df):,} registros")
    
    return df


def _remove_returns(df, verbose=False):
    """Remove devoluções de mercadoria."""
    initial_count = len(df)
    df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']
    
    if verbose:
        removed = initial_count - len(df)
        print(f"Remoção DEVOLUCAO DE MERCADORIA: {removed:,} registros removidos")
    
    return df


def _validate_prices(df, verbose=False):
    """Valida preços mínimos."""
    initial_count = len(df)
    
    # Preço Varejo > 1
    df = df[df['Preco_Varejo'] > 1]
    after_preco = len(df)
    
    # Total Preço Varejo > 1
    df = df[df['Total_Preco_Varejo'] > 1]
    after_total = len(df)
    
    if verbose:
        removed_preco = initial_count - after_preco
        removed_total = after_preco - after_total
        print(f"Preço Varejo > 1: {removed_preco:,} registros removidos")
        print(f"Total Preço Varejo > 1: {removed_total:,} registros removidos")
    
    return df


def _validate_total_calculations(df, verbose=False):
    """Valida cálculos de valor total."""
    initial_count = len(df)
    
    # Validação inicial: Quantidade * Preco_Varejo - DESCONTO_CALCULADO = Valor_Total
    df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
    df = df[df['diferenca'] < 1]
    after_first = len(df)
    
    # Validação com tolerância maior
    df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']
    df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]
    after_second = len(df)
    
    if verbose:
        removed_first = initial_count - after_first
        removed_second = after_first - after_second
        print(f"Validação cálculo valor total: {removed_first:,} registros removidos")
        print(f"Validação valor total (tolerância 2): {removed_second:,} registros removidos")
    
    return df


def _validate_customer_ages(df, verbose=False):
    """Valida idades dos clientes."""
    initial_count = len(df)

    # Fazer uma cópia para evitar warnings do pandas
    df = df.copy()

    # Converter datas
    df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')
    df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')
    df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')

    # Calcular idade na data da venda
    df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
    df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]
    after_age = len(df)

    # Calcular idade no cadastro
    df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
    df = df[df['idade_cadastro'] >= 18]
    after_registration = len(df)

    if verbose:
        removed_age = initial_count - after_age
        removed_registration = after_age - after_registration
        print(f"Idade entre 10 e 100 anos: {removed_age:,} registros removidos")
        print(f"Idade >= 18 anos no cadastro: {removed_registration:,} registros removidos")

    return df


def _remove_duplicates(df, verbose=False):
    """Remove registros duplicados."""
    initial_count = len(df)
    df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])
    
    if verbose:
        removed = initial_count - len(df)
        print(f"Remoção duplicatas: {removed:,} registros removidos")
    
    return df


def _validate_stores_and_products(df, verbose=False):
    """Valida lojas e produtos."""
    initial_count = len(df)
    
    # Validar lojas
    df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]
    after_stores = len(df)
    
    # Validar produtos
    df = df[df['Dim_Produtos.Nome'].notnull()]
    after_product_name = len(df)
    
    df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]
    after_product_group = len(df)
    
    if verbose:
        removed_stores = initial_count - after_stores
        removed_product_name = after_stores - after_product_name
        removed_product_group = after_product_name - after_product_group
        print(f"Lojas com Tipo PDV válido: {removed_stores:,} registros removidos")
        print(f"Produtos com nome válido: {removed_product_name:,} registros removidos")
        print(f"Produtos com grupo válido: {removed_product_group:,} registros removidos")
    
    return df


def _clean_string_columns(df, verbose=False):
    """Limpa colunas de string removendo espaços em branco."""
    df['Dim_Lojas.Tipo_PDV'] = df['Dim_Lojas.Tipo_PDV'].astype(str).str.strip()
    df['Dim_Produtos.Sub_Grupo'] = df['Dim_Produtos.Sub_Grupo'].astype(str).str.strip()
    
    if verbose:
        print("Limpeza de espaços em branco nas colunas categóricas concluída")
    
    return df


def get_filtering_summary(csv_path):
    """
    Retorna um resumo detalhado dos filtros aplicados.
    
    Args:
        csv_path (str): Caminho para o arquivo CSV com os dados
        
    Returns:
        pd.DataFrame: DataFrame com resumo dos filtros aplicados
    """
    
    # Carregar dados originais
    df_original = pd.read_csv(csv_path)
    rows_inicial = len(df_original)
    
    # Aplicar filtros com tracking
    filtros_aplicados = []
    filtros_aplicados.append(('Dados iniciais', rows_inicial, 0))
    
    df = df_original.copy()
    
    # 1. Remoção de devoluções
    df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']
    rows_after = len(df)
    filtros_aplicados.append(('Remoção DEVOLUCAO DE MERCADORIA', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 2. Preços
    df = df[df['Preco_Varejo'] > 1]
    rows_after = len(df)
    filtros_aplicados.append(('Preço Varejo > 1', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    df = df[df['Total_Preco_Varejo'] > 1]
    rows_after = len(df)
    filtros_aplicados.append(('Total Preço Varejo > 1', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 3. Validação de cálculos
    df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
    df = df[df['diferenca'] < 1]
    rows_after = len(df)
    filtros_aplicados.append(('Validação cálculo valor total', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 4. Idades
    df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')
    df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')
    df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')
    
    df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
    df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]
    rows_after = len(df)
    filtros_aplicados.append(('Idade entre 10 e 100 anos', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
    df = df[df['idade_cadastro'] >= 18]
    rows_after = len(df)
    filtros_aplicados.append(('Idade >= 18 anos no cadastro', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 5. Duplicatas
    df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])
    rows_after = len(df)
    filtros_aplicados.append(('Remoção duplicatas', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 6. Validação com tolerância
    df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']
    df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]
    rows_after = len(df)
    filtros_aplicados.append(('Validação valor total (tolerância 2)', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    # 7. Lojas e produtos
    df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]
    rows_after = len(df)
    filtros_aplicados.append(('Lojas com Tipo PDV válido', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    df = df[df['Dim_Produtos.Nome'].notnull()]
    rows_after = len(df)
    filtros_aplicados.append(('Produtos com nome válido', rows_after, rows_inicial - rows_after))
    rows_inicial = rows_after
    
    df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]
    rows_after = len(df)
    filtros_aplicados.append(('Produtos com grupo válido', rows_after, rows_inicial - rows_after))
    
    # Criar DataFrame de resumo
    df_filtros = pd.DataFrame(filtros_aplicados, columns=['Filtro Aplicado', 'Linhas Restantes', 'Linhas Removidas'])
    rows_inicial_total = len(df_original)
    df_filtros['% Removido'] = (df_filtros['Linhas Removidas'] / rows_inicial_total * 100).round(2)
    df_filtros['% Restante'] = (df_filtros['Linhas Restantes'] / rows_inicial_total * 100).round(2)
    
    return df_filtros
